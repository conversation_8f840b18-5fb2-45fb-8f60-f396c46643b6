"""
Admin API endpoints for the Datagenius backend.

This module provides API endpoints for admin-specific functionality.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from ..models.admin import (
    AdminActivityLogListResponse,
    AdminUserUpdateRequest, AdminPersonaUpdateRequest, AdminPersonaCreateRequest,
    AdminDashboardStats, AdminUserListResponse, AdminAnalyticsRequest,
    AdminAnalyticsResponse
)
from ..models.auth import User
from ..models.persona import (
    PersonaResponse, PersonaListResponse,
    PersonaVersionCreate, PersonaVersionResponse, PersonaVersionListResponse
)
from ..dependencies import (
    get_user_repository, get_admin_activity_log_repository,
    get_persona_repository, get_persona_version_repository,
    get_purchase_repository
)
from ..repositories.user_repository import UserRepository
from ..repositories.admin_activity_log_repository import AdminActivityLogRepository
from ..repositories.persona_repository import PersonaRepository
from ..repositories.persona_version_repository import PersonaVersionRepository
from ..repositories.purchase_repository import PurchaseRepository
from ..auth import get_current_active_user

# Import the agent factory and persona manager using centralized import utility
from ..utils.import_utils import import_agent_factory, import_persona_manager

agent_factory = import_agent_factory()
PersonaManager = import_persona_manager()

# Import deployment workflow using centralized import utility
from ..utils.import_utils import import_from_backend
DeploymentWorkflow = import_from_backend('agents.tools.deployment', 'DeploymentWorkflow')

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/admin", tags=["Admin"])


# Admin middleware
async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
):
    """
    Get the current admin user.

    Args:
        current_user: Current authenticated user

    Returns:
        User object if the user is an admin

    Raises:
        HTTPException: If the user is not an admin
    """
    if not current_user.is_superuser:
        logger.warning(f"Non-admin user {current_user.id} attempted to access admin endpoint")
        raise HTTPException(status_code=403, detail="Not authorized to access admin functionality")
    return current_user


# Dashboard endpoints
@router.get("/dashboard/stats", response_model=AdminDashboardStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get dashboard statistics.

    Args:
        db: Database session
        current_user: Current admin user

    Returns:
        Dashboard statistics
    """
    logger.info(f"Admin {current_user.id} getting dashboard stats")

    # Get statistics from database
    total_users = get_user_count(db)
    active_users = get_user_count(db, is_active=True)
    total_personas = get_persona_count(db)
    active_personas = get_persona_count(db, is_active=True)
    total_purchases = get_purchase_count(db)
    total_revenue = get_total_revenue(db)

    # Get recent statistics (last 30 days)
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    recent_purchases = get_recent_purchases(db, thirty_days_ago)
    recent_revenue = get_recent_revenue(db, thirty_days_ago)

    return AdminDashboardStats(
        total_users=total_users,
        active_users=active_users,
        total_personas=total_personas,
        active_personas=active_personas,
        total_purchases=total_purchases,
        total_revenue=total_revenue,
        recent_purchases=recent_purchases,
        recent_revenue=recent_revenue
    )


# User management endpoints
@router.get("/users", response_model=AdminUserListResponse)
async def list_users(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    is_superuser: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    List users.

    Args:
        skip: Number of users to skip
        limit: Maximum number of users to return
        search: Search term for email or username
        is_active: Filter by active status
        is_superuser: Filter by superuser status
        db: Database session
        current_user: Current admin user

    Returns:
        List of users
    """
    logger.info(f"Admin {current_user.id} listing users")

    users, total = get_users(
        db,
        skip=skip,
        limit=limit,
        search=search,
        is_active=is_active,
        is_superuser=is_superuser
    )

    return {
        "users": users,
        "total": total
    }


@router.get("/users/{user_id}", response_model=User)
async def get_user(
    user_id: int = Path(..., title="The ID of the user to get"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get a user.

    Args:
        user_id: ID of the user
        db: Database session
        current_user: Current admin user

    Returns:
        User information
    """
    logger.info(f"Admin {current_user.id} getting user {user_id}")

    user = get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=f"User {user_id} not found")

    return user


@router.put("/users/{user_id}", response_model=User)
async def update_user_info(
    user_id: int = Path(..., title="The ID of the user to update"),
    user_data: AdminUserUpdateRequest = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Update a user.

    Args:
        user_id: ID of the user
        user_data: User data to update
        db: Database session
        current_user: Current admin user

    Returns:
        Updated user information
    """
    logger.info(f"Admin {current_user.id} updating user {user_id}")

    # Check if the user exists
    user = get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=f"User {user_id} not found")

    # Update the user
    updated_user = update_user(db, user_id, user_data.model_dump(exclude_unset=True))

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="update_user",
        entity_type="user",
        entity_id=str(user_id),
        details=user_data.model_dump(exclude_unset=True)
    )

    return updated_user


# Persona management endpoints
@router.get("/personas", response_model=PersonaListResponse)
async def list_personas_admin(
    skip: int = 0,
    limit: int = 100,
    industry: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    List all personas with admin details.

    Args:
        skip: Number of personas to skip
        limit: Maximum number of personas to return
        industry: Filter by industry
        is_active: Filter by active status
        db: Database session
        current_user: Current admin user

    Returns:
        List of personas
    """
    logger.info(f"Admin {current_user.id} listing personas")

    # Get personas from database or registry
    personas = get_personas(db, skip, limit, industry, is_active)

    return {"personas": personas}


@router.get("/personas/{persona_id}", response_model=PersonaResponse)
async def get_persona_admin(
    persona_id: str = Path(..., title="The ID of the persona to get"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get a specific persona with admin details.

    Args:
        persona_id: ID of the persona
        db: Database session
        current_user: Current admin user

    Returns:
        Persona information
    """
    logger.info(f"Admin {current_user.id} getting persona {persona_id}")

    # Get persona from database or registry
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    return {"persona": persona}


@router.post("/personas", response_model=PersonaResponse)
async def create_persona_admin(
    persona_data: AdminPersonaCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Create a new persona.

    Args:
        persona_data: Persona data to create
        db: Database session
        current_user: Current admin user

    Returns:
        Created persona information
    """
    logger.info(f"Admin {current_user.id} creating persona {persona_data.id}")

    # Check if the persona already exists
    existing_persona = get_persona(db, persona_data.id)
    if existing_persona:
        raise HTTPException(status_code=400, detail=f"Persona {persona_data.id} already exists")

    # If provider and model are specified, validate that the model exists
    if persona_data.provider and persona_data.model:
        try:
            # Import the model provider system
            from agents.utils.model_providers.utils import list_available_models

            # Get available models for the provider
            models_by_provider = await list_available_models()

            # Check if the provider exists
            provider_id = persona_data.provider.lower()
            if provider_id not in models_by_provider:
                logger.warning(f"Provider '{provider_id}' not found in available providers")
                raise HTTPException(
                    status_code=400,
                    detail=f"Provider '{provider_id}' is not available. Please choose from: {', '.join(models_by_provider.keys())}"
                )

            # Check if the model exists for the provider
            provider_models = models_by_provider[provider_id]
            model_exists = any(model["id"] == persona_data.model for model in provider_models)

            if not model_exists:
                logger.warning(f"Model '{persona_data.model}' not found for provider '{provider_id}'")
                available_models = [model["id"] for model in provider_models]
                raise HTTPException(
                    status_code=400,
                    detail=f"Model '{persona_data.model}' is not available for provider '{provider_id}'. Please choose from: {', '.join(available_models[:10])}{'...' if len(available_models) > 10 else ''}"
                )

            logger.info(f"Validated model '{persona_data.model}' for provider '{provider_id}'")
        except ImportError as e:
            logger.error(f"Error importing model provider system: {str(e)}", exc_info=True)
            # Continue without validation if the model provider system is not available
        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            logger.error(f"Error validating model: {str(e)}", exc_info=True)
            # Continue without validation if there's an error

    # Create the persona
    persona = create_persona(db, persona_data.model_dump())

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="create_persona",
        entity_type="persona",
        entity_id=persona_data.id,
        details=persona_data.model_dump()
    )

    return {"persona": persona}


@router.put("/personas/{persona_id}", response_model=PersonaResponse)
async def update_persona_admin(
    persona_id: str = Path(..., title="The ID of the persona to update"),
    persona_data: AdminPersonaUpdateRequest = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Update a persona.

    Args:
        persona_id: ID of the persona
        persona_data: Persona data to update
        db: Database session
        current_user: Current admin user

    Returns:
        Updated persona information
    """
    logger.info(f"Admin {current_user.id} updating persona {persona_id}")

    # Check if the persona exists
    existing_persona = get_persona(db, persona_id)
    if not existing_persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # If provider or model is being updated, validate that the model exists
    if persona_data.provider is not None or persona_data.model is not None:
        try:
            # Import the model provider system
            from agents.utils.model_providers.utils import list_available_models

            # Get the provider (either the new one or the existing one)
            provider_id = persona_data.provider if persona_data.provider is not None else existing_persona.provider

            # Get the model (either the new one or the existing one)
            model_id = persona_data.model if persona_data.model is not None else existing_persona.model

            if provider_id and model_id:
                # Get available models for the provider
                models_by_provider = await list_available_models()

                # Check if the provider exists
                if provider_id.lower() not in models_by_provider:
                    logger.warning(f"Provider '{provider_id}' not found in available providers")
                    raise HTTPException(
                        status_code=400,
                        detail=f"Provider '{provider_id}' is not available. Please choose from: {', '.join(models_by_provider.keys())}"
                    )

                # Check if the model exists for the provider
                provider_models = models_by_provider[provider_id.lower()]
                model_exists = any(model["id"] == model_id for model in provider_models)

                if not model_exists:
                    logger.warning(f"Model '{model_id}' not found for provider '{provider_id}'")
                    available_models = [model["id"] for model in provider_models]
                    raise HTTPException(
                        status_code=400,
                        detail=f"Model '{model_id}' is not available for provider '{provider_id}'. Please choose from: {', '.join(available_models[:10])}{'...' if len(available_models) > 10 else ''}"
                    )

                logger.info(f"Validated model '{model_id}' for provider '{provider_id}'")
        except ImportError as e:
            logger.error(f"Error importing model provider system: {str(e)}", exc_info=True)
            # Continue without validation if the model provider system is not available
        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            logger.error(f"Error validating model: {str(e)}", exc_info=True)
            # Continue without validation if there's an error

    # Update the persona
    persona = update_persona(db, persona_id, persona_data.model_dump(exclude_unset=True))

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="update_persona",
        entity_type="persona",
        entity_id=persona_id,
        details=persona_data.model_dump(exclude_unset=True)
    )

    return {"persona": persona}


@router.delete("/personas/{persona_id}", response_model=dict)
async def delete_persona_admin(
    persona_id: str = Path(..., title="The ID of the persona to delete"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Delete a persona.

    Args:
        persona_id: ID of the persona
        db: Database session
        current_user: Current admin user

    Returns:
        Success message
    """
    logger.info(f"Admin {current_user.id} deleting persona {persona_id}")

    # Check if the persona exists
    existing_persona = get_persona(db, persona_id)
    if not existing_persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Delete the persona
    success = delete_persona(db, persona_id)
    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to delete persona {persona_id}")

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="delete_persona",
        entity_type="persona",
        entity_id=persona_id
    )

    return {"message": f"Persona {persona_id} deleted successfully"}


# Persona version management endpoints
@router.get("/personas/{persona_id}/versions", response_model=PersonaVersionListResponse)
async def list_persona_versions(
    persona_id: str = Path(..., title="The ID of the persona to get versions for"),
    skip: int = 0,
    limit: int = 100,
    include_file_versions: bool = Query(False, title="Whether to include versions from files"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    List all versions for a persona.

    Args:
        persona_id: ID of the persona
        skip: Number of versions to skip
        limit: Maximum number of versions to return
        include_file_versions: Whether to include versions from files
        db: Database session
        current_user: Current admin user

    Returns:
        List of persona versions
    """
    logger.info(f"Admin {current_user.id} listing versions for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Get versions from database
    versions = get_persona_versions(db, persona_id, skip, limit)

    # If requested, also get versions from files
    if include_file_versions:
        # Get file versions
        file_versions = DeploymentWorkflow.list_versions(
            persona_id=persona_id,
            personas_dir="personas"
        )

        # Add file versions that aren't in the database
        db_version_strings = [v.version for v in versions]
        for version_str in file_versions:
            if version_str not in db_version_strings:
                # Create a minimal version object for the file version
                versions.append({
                    "id": f"{persona_id}-{version_str}",
                    "persona_id": persona_id,
                    "version": version_str,
                    "config": {},  # Empty config as we don't load it here
                    "is_active": False,
                    "created_at": None,
                    "created_by": None,
                    "is_file_only": True  # Flag to indicate this is only in files
                })

    return {"versions": versions}


@router.get("/personas/{persona_id}/versions/{version}", response_model=PersonaVersionResponse)
async def get_persona_version_admin(
    persona_id: str = Path(..., title="The ID of the persona"),
    version: str = Path(..., title="The version string"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get a specific version of a persona.

    Args:
        persona_id: ID of the persona
        version: Version string
        db: Database session
        current_user: Current admin user

    Returns:
        Persona version information
    """
    logger.info(f"Admin {current_user.id} getting version {version} for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Get version from database
    version_id = f"{persona_id}-{version}"
    persona_version = get_persona_version(db, version_id)
    if not persona_version:
        raise HTTPException(status_code=404, detail=f"Version {version} for persona {persona_id} not found")

    return {"version": persona_version}


@router.post("/personas/{persona_id}/versions", response_model=PersonaVersionResponse)
async def create_persona_version_admin(
    persona_id: str = Path(..., title="The ID of the persona"),
    version_data: PersonaVersionCreate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Create a new version of a persona.

    Args:
        persona_id: ID of the persona
        version_data: Version data to create
        db: Database session
        current_user: Current admin user

    Returns:
        Created persona version information
    """
    logger.info(f"Admin {current_user.id} creating version {version_data.version} for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Check if the version already exists
    version_id = f"{persona_id}-{version_data.version}"
    existing_version = get_persona_version(db, version_id)
    if existing_version:
        raise HTTPException(status_code=400, detail=f"Version {version_data.version} for persona {persona_id} already exists")

    # Create the version in the database
    version = create_persona_version(db, {
        "id": version_id,
        "persona_id": persona_id,
        "version": version_data.version,
        "config": version_data.config,
        "is_active": version_data.is_active,
        "created_by": current_user.id
    })

    # Create the version file on disk
    success = DeploymentWorkflow.create_version(
        persona_id=persona_id,
        version=version_data.version,
        config=version_data.config,
        personas_dir="personas"
    )

    if not success:
        logger.error(f"Failed to create version file for {persona_id} version {version_data.version}")
        # Continue anyway, as the database version is created

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="create_persona_version",
        entity_type="persona_version",
        entity_id=version_id,
        details=version_data.model_dump()
    )

    # If this version is active, update the main persona configuration
    if version.is_active:
        # Update the persona with the new configuration
        update_data = {
            "name": version.config.get("name", persona.name),
            "description": version.config.get("description", persona.description),
            "industry": version.config.get("industry", persona.industry),
            "skills": version.config.get("skills", persona.skills),
            "provider": version.config.get("provider", persona.provider),
            "model": version.config.get("model", persona.model),
            "price": version.config.get("price", persona.price),
            "is_active": version.config.get("is_active", persona.is_active),
            "age_restriction": version.config.get("age_restriction", persona.age_restriction),
            "content_filters": version.config.get("content_filters", persona.content_filters)
        }
        update_persona(db, persona_id, update_data)

        # Activate the version file on disk
        success = DeploymentWorkflow.activate_version(
            persona_id=persona_id,
            version=version_data.version,
            personas_dir="personas"
        )

        if not success:
            logger.error(f"Failed to activate version file for {persona_id} version {version_data.version}")

        # Note: The new LangGraph agent factory auto-discovers configurations
        # No explicit reloading needed
        logger.info(f"Configuration activated for persona {persona_id} version {version_data.version}")

    return {"version": version}


@router.put("/personas/{persona_id}/versions/{version}/activate", response_model=PersonaVersionResponse)
async def activate_persona_version_admin(
    persona_id: str = Path(..., title="The ID of the persona"),
    version: str = Path(..., title="The version string"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Activate a specific version of a persona.

    Args:
        persona_id: ID of the persona
        version: Version string
        db: Database session
        current_user: Current admin user

    Returns:
        Activated persona version information
    """
    logger.info(f"Admin {current_user.id} activating version {version} for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Get version from database
    version_id = f"{persona_id}-{version}"
    persona_version = get_persona_version(db, version_id)
    if not persona_version:
        raise HTTPException(status_code=404, detail=f"Version {version} for persona {persona_id} not found")

    # Activate the version in the database
    activated_version = activate_persona_version(db, version_id)

    # Activate the version file on disk
    success = DeploymentWorkflow.activate_version(
        persona_id=persona_id,
        version=version,
        personas_dir="personas"
    )

    if not success:
        logger.warning(f"Failed to activate version file for {persona_id} version {version}")
        # Continue anyway, as the database version is activated

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="activate_persona_version",
        entity_type="persona_version",
        entity_id=version_id
    )

    # Update the persona with the new configuration
    update_data = {
        "name": activated_version.config.get("name", persona.name),
        "description": activated_version.config.get("description", persona.description),
        "industry": activated_version.config.get("industry", persona.industry),
        "skills": activated_version.config.get("skills", persona.skills),
        "provider": activated_version.config.get("provider", persona.provider),
        "model": activated_version.config.get("model", persona.model),
        "price": activated_version.config.get("price", persona.price),
        "is_active": activated_version.config.get("is_active", persona.is_active),
        "age_restriction": activated_version.config.get("age_restriction", persona.age_restriction),
        "content_filters": activated_version.config.get("content_filters", persona.content_filters)
    }
    update_persona(db, persona_id, update_data)

    # Note: The new LangGraph agent factory auto-discovers configurations
    # No explicit reloading needed
    logger.info(f"Configuration activated for persona {persona_id} version {version}")

    return {"version": activated_version}


@router.delete("/personas/{persona_id}/versions/{version}", response_model=dict)
async def delete_persona_version_admin(
    persona_id: str = Path(..., title="The ID of the persona"),
    version: str = Path(..., title="The version string"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Delete a specific version of a persona.

    Args:
        persona_id: ID of the persona
        version: Version string
        db: Database session
        current_user: Current admin user

    Returns:
        Success message
    """
    logger.info(f"Admin {current_user.id} deleting version {version} for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Get version from database
    version_id = f"{persona_id}-{version}"
    persona_version = get_persona_version(db, version_id)
    if not persona_version:
        raise HTTPException(status_code=404, detail=f"Version {version} for persona {persona_id} not found")

    # Check if this is the active version
    if persona_version.is_active:
        raise HTTPException(status_code=400, detail=f"Cannot delete active version {version} for persona {persona_id}")

    # Delete the version from the database
    success = delete_persona_version(db, version_id)
    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to delete version {version} for persona {persona_id}")

    # Delete the version file on disk
    file_success = DeploymentWorkflow.delete_version(
        persona_id=persona_id,
        version=version,
        personas_dir="personas"
    )

    if not file_success:
        logger.warning(f"Failed to delete version file for {persona_id} version {version}")
        # Continue anyway, as the database version is deleted

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="delete_persona_version",
        entity_type="persona_version",
        entity_id=version_id
    )

    return {"message": f"Version {version} for persona {persona_id} deleted successfully"}


# Version rollback endpoint
@router.post("/personas/{persona_id}/versions/{version}/rollback", response_model=PersonaVersionResponse)
async def rollback_persona_version(
    persona_id: str = Path(..., title="The ID of the persona"),
    version: str = Path(..., title="The version string to roll back to"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Roll back to a previous version of a persona.

    Args:
        persona_id: ID of the persona
        version: Version string to roll back to
        db: Database session
        current_user: Current admin user

    Returns:
        Activated persona version information
    """
    logger.info(f"Admin {current_user.id} rolling back to version {version} for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Get version from database
    version_id = f"{persona_id}-{version}"
    persona_version = get_persona_version(db, version_id)
    if not persona_version:
        raise HTTPException(status_code=404, detail=f"Version {version} for persona {persona_id} not found")

    # Roll back to the version (same as activating it)
    success = DeploymentWorkflow.rollback_version(
        persona_id=persona_id,
        version=version,
        personas_dir="personas"
    )

    if not success:
        logger.warning(f"Failed to roll back to version file for {persona_id} version {version}")
        # Continue anyway with database rollback

    # Activate the version in the database
    activated_version = activate_persona_version(db, version_id)

    # Log the activity
    log_admin_activity(
        db,
        admin_id=current_user.id,
        action="rollback_persona_version",
        entity_type="persona_version",
        entity_id=version_id
    )

    # Update the persona with the rolled back configuration
    update_data = {
        "name": activated_version.config.get("name", persona.name),
        "description": activated_version.config.get("description", persona.description),
        "industry": activated_version.config.get("industry", persona.industry),
        "skills": activated_version.config.get("skills", persona.skills),
        "provider": activated_version.config.get("provider", persona.provider),
        "model": activated_version.config.get("model", persona.model),
        "price": activated_version.config.get("price", persona.price),
        "is_active": activated_version.config.get("is_active", persona.is_active),
        "age_restriction": activated_version.config.get("age_restriction", persona.age_restriction),
        "content_filters": activated_version.config.get("content_filters", persona.content_filters)
    }
    update_persona(db, persona_id, update_data)

    # Note: The new LangGraph agent factory auto-discovers configurations
    # No explicit reloading needed
    logger.info(f"Configuration rolled back for persona {persona_id} to version {version}")

    return {"version": activated_version}


# Version comparison endpoint
@router.get("/personas/{persona_id}/versions/compare", response_model=Dict[str, Any])
async def compare_persona_versions(
    persona_id: str = Path(..., title="The ID of the persona"),
    version1: str = Query(..., title="First version to compare"),
    version2: str = Query(..., title="Second version to compare"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Compare two versions of a persona.

    Args:
        persona_id: ID of the persona
        version1: First version string
        version2: Second version string
        db: Database session
        current_user: Current admin user

    Returns:
        Dictionary with differences between the versions
    """
    logger.info(f"Admin {current_user.id} comparing versions {version1} and {version2} for persona {persona_id}")

    # Check if the persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Get versions from database
    version1_id = f"{persona_id}-{version1}"
    version2_id = f"{persona_id}-{version2}"

    persona_version1 = get_persona_version(db, version1_id)
    if not persona_version1:
        raise HTTPException(status_code=404, detail=f"Version {version1} for persona {persona_id} not found")

    persona_version2 = get_persona_version(db, version2_id)
    if not persona_version2:
        raise HTTPException(status_code=404, detail=f"Version {version2} for persona {persona_id} not found")

    # Compare the versions
    differences = DeploymentWorkflow.compare_versions(
        persona_id=persona_id,
        version1=version1,
        version2=version2,
        personas_dir="personas"
    )

    # If no differences found in files, compare database versions
    if not differences:
        differences = {}

        # Compare configurations
        for key in persona_version1.config:
            if key not in persona_version2.config:
                differences[key] = {
                    "version1": persona_version1.config[key],
                    "version2": None
                }
            elif persona_version1.config[key] != persona_version2.config[key]:
                differences[key] = {
                    "version1": persona_version1.config[key],
                    "version2": persona_version2.config[key]
                }

        # Find keys that are in version2 but not in version1
        for key in persona_version2.config:
            if key not in persona_version1.config:
                differences[key] = {
                    "version1": None,
                    "version2": persona_version2.config[key]
                }

    return differences


# Activity log endpoints
@router.get("/activity-logs", response_model=AdminActivityLogListResponse)
async def list_activity_logs(
    skip: int = 0,
    limit: int = 100,
    action: Optional[str] = None,
    entity_type: Optional[str] = None,
    admin_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    List admin activity logs.

    Args:
        skip: Number of logs to skip
        limit: Maximum number of logs to return
        action: Filter by action
        entity_type: Filter by entity type
        admin_id: Filter by admin ID
        db: Database session
        current_user: Current admin user

    Returns:
        List of activity logs
    """
    logger.info(f"Admin {current_user.id} listing activity logs")

    logs, total = get_admin_activity_logs(
        db,
        skip=skip,
        limit=limit,
        action=action,
        entity_type=entity_type,
        admin_id=admin_id
    )

    return {
        "logs": logs,
        "total": total
    }


# Analytics endpoints
@router.post("/analytics/purchases", response_model=AdminAnalyticsResponse)
async def get_purchase_analytics(
    _request: AdminAnalyticsRequest,
    _db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get purchase analytics.

    Args:
        request: Analytics request
        db: Database session
        current_user: Current admin user

    Returns:
        Purchase analytics
    """
    logger.info(f"Admin {current_user.id} getting purchase analytics")

    # This would be implemented with actual database queries
    # For now, return mock data
    return {
        "data": [
            {"date": "2023-01-01", "value": 100.0},
            {"date": "2023-01-02", "value": 150.0},
            {"date": "2023-01-03", "value": 200.0},
        ],
        "total": 450.0,
        "average": 150.0,
        "min": 100.0,
        "max": 200.0
    }


@router.post("/analytics/users", response_model=AdminAnalyticsResponse)
async def get_user_analytics(
    _request: AdminAnalyticsRequest,
    _db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get user analytics.

    Args:
        request: Analytics request
        db: Database session
        current_user: Current admin user

    Returns:
        User analytics
    """
    logger.info(f"Admin {current_user.id} getting user analytics")

    # This would be implemented with actual database queries
    # For now, return mock data
    return {
        "data": [
            {"date": "2023-01-01", "value": 10.0},
            {"date": "2023-01-02", "value": 15.0},
            {"date": "2023-01-03", "value": 20.0},
        ],
        "total": 45.0,
        "average": 15.0,
        "min": 10.0,
        "max": 20.0
    }


@router.post("/analytics/personas", response_model=AdminAnalyticsResponse)
async def get_persona_analytics(
    _request: AdminAnalyticsRequest,
    _db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get persona analytics.

    Args:
        request: Analytics request
        db: Database session
        current_user: Current admin user

    Returns:
        Persona analytics
    """
    logger.info(f"Admin {current_user.id} getting persona analytics")

    # This would be implemented with actual database queries
    # For now, return mock data
    return {
        "data": [
            {"date": "2023-01-01", "value": 5.0},
            {"date": "2023-01-02", "value": 7.0},
            {"date": "2023-01-03", "value": 9.0},
        ],
        "total": 21.0,
        "average": 7.0,
        "min": 5.0,
        "max": 9.0
    }
