"""
Repository Pattern Implementation for Datagenius.

This package provides a unified data access layer using the repository pattern
to replace the 63 duplicate CRUD functions identified in the technical debt analysis.

Phase 1 Implementation:
- BaseRepository[T] generic pattern for type-safe data access
- Specialized repositories for core entities
- DatabaseService abstraction layer
- Repository-level caching and optimization
- Security enhancements with parameterized queries
- Database query auditing and monitoring
"""

from .base_repository import BaseRepository, RepositoryError
from .database_service import DatabaseService
from .user_repository import UserRepository
from .conversation_repository import ConversationRepository
from .business_profile_repository import BusinessProfileRepository
from .agent_repository import AgentRepository

__all__ = [
    "BaseRepository",
    "RepositoryError", 
    "DatabaseService",
    "UserRepository",
    "ConversationRepository", 
    "BusinessProfileRepository",
    "AgentRepository"
]
